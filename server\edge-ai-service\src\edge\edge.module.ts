import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';

import { EdgeAIService } from './edge-ai.service';
import { EdgeController } from './edge.controller';
import { EdgeGateway } from './edge.gateway';

// 实体导入
import { EdgeDevice } from '../device/entities/edge-device.entity';
import { AIModel } from '../model/entities/ai-model.entity';
import { InferenceRequest } from '../inference/entities/inference-request.entity';
import { InferenceResult } from '../inference/entities/inference-result.entity';
import { LearningTask } from '../learning/entities/learning-task.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      EdgeDevice,
      AIModel,
      InferenceRequest,
      InferenceResult,
      LearningTask,
    ]),
    BullModule.registerQueue({
      name: 'edge-inference',
    }),
    BullModule.registerQueue({
      name: 'model-deployment',
    }),
    BullModule.registerQueue({
      name: 'distributed-learning',
    }),
  ],
  controllers: [EdgeController],
  providers: [EdgeAIService, EdgeGateway],
  exports: [EdgeAIService],
})
export class EdgeModule {}
