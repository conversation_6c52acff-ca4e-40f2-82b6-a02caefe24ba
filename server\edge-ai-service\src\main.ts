import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as helmet from 'helmet';
import * as compression from 'compression';
import * as cors from 'cors';
import { AppModule } from './app.module';

async function bootstrap() {
  // 创建HTTP应用
  const app = await NestFactory.create(AppModule);

  // 安全中间件
  app.use(helmet());
  app.use(compression());
  app.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true
  }));

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
    transformOptions: {
      enableImplicitConversion: true
    }
  }));

  // 全局前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('边缘AI计算服务')
    .setDescription('边缘设备AI推理、分布式学习、实时决策优化服务API')
    .setVersion('1.0')
    .addTag('edge-ai', '边缘AI计算')
    .addTag('devices', '边缘设备管理')
    .addTag('models', 'AI模型管理')
    .addTag('inference', '推理服务')
    .addTag('learning', '分布式学习')
    .addTag('optimization', '实时优化')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 微服务配置
  const microservice = app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.REDIS,
    options: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
      retryAttempts: 5,
      retryDelay: 3000,
    },
  });

  // 启动微服务
  await app.startAllMicroservices();

  // 启动HTTP服务器
  const port = process.env.PORT || 3006;
  await app.listen(port);

  console.log(`🚀 边缘AI计算服务已启动`);
  console.log(`📖 API文档: http://localhost:${port}/api/docs`);
  console.log(`🔗 服务地址: http://localhost:${port}/api/v1`);
  console.log(`📡 微服务: Redis Transport`);
}

bootstrap().catch(error => {
  console.error('启动边缘AI计算服务失败:', error);
  process.exit(1);
});
