import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InferenceService } from './inference.service';
import { InferenceController } from './inference.controller';
import { InferenceRequest } from './entities/inference-request.entity';
import { InferenceResult } from './entities/inference-result.entity';

@Module({
  imports: [TypeOrmModule.forFeature([InferenceRequest, InferenceResult])],
  controllers: [InferenceController],
  providers: [InferenceService],
  exports: [InferenceService],
})
export class InferenceModule {}
