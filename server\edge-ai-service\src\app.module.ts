import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';

// 模块导入
import { EdgeModule } from './edge/edge.module';
import { DeviceModule } from './device/device.module';
import { ModelModule } from './model/model.module';
import { InferenceModule } from './inference/inference.module';
import { LearningModule } from './learning/learning.module';
import { OptimizationModule } from './optimization/optimization.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { WebSocketModule } from './websocket/websocket.module';

// 实体导入
import { EdgeDevice } from './device/entities/edge-device.entity';
import { AIModel } from './model/entities/ai-model.entity';
import { InferenceRequest } from './inference/entities/inference-request.entity';
import { InferenceResult } from './inference/entities/inference-result.entity';
import { LearningTask } from './learning/entities/learning-task.entity';
import { DevicePerformance } from './monitoring/entities/device-performance.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', ''),
        database: configService.get('DB_DATABASE', 'edge_ai_service'),
        entities: [
          EdgeDevice,
          AIModel,
          InferenceRequest,
          InferenceResult,
          LearningTask,
          DevicePerformance,
        ],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        timezone: '+08:00',
        charset: 'utf8mb4',
      }),
      inject: [ConfigService],
    }),

    // 任务调度模块
    ScheduleModule.forRoot(),

    // 队列模块
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
        },
      }),
      inject: [ConfigService],
    }),

    // 业务模块
    EdgeModule,
    DeviceModule,
    ModelModule,
    InferenceModule,
    LearningModule,
    OptimizationModule,
    MonitoringModule,
    WebSocketModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
