import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { EdgeAIService } from './edge-ai.service';
import {
  RegisterDeviceDto,
  DeployModelDto,
  InferenceRequestDto,
  DistributedLearningTaskDto,
  OptimizationRequestDto,
} from './dto';

@ApiTags('edge-ai')
@Controller('edge')
export class EdgeController {
  private readonly logger = new Logger(EdgeController.name);

  constructor(private readonly edgeAIService: EdgeAIService) {}

  @Post('devices/register')
  @ApiOperation({ summary: '注册边缘设备' })
  @ApiBody({ type: RegisterDeviceDto })
  @ApiResponse({ status: 201, description: '设备注册成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async registerDevice(@Body() registerDeviceDto: RegisterDeviceDto) {
    try {
      const deviceId = await this.edgeAIService.registerEdgeDevice(registerDeviceDto);
      return {
        success: true,
        data: { deviceId },
        message: '边缘设备注册成功',
      };
    } catch (error) {
      this.logger.error('注册边缘设备失败', error);
      throw new HttpException(
        {
          success: false,
          message: '注册边缘设备失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('models/deploy')
  @ApiOperation({ summary: '部署AI模型到边缘设备' })
  @ApiBody({ type: DeployModelDto })
  @ApiResponse({ status: 201, description: '模型部署成功' })
  @ApiResponse({ status: 400, description: '部署失败' })
  async deployModel(@Body() deployModelDto: DeployModelDto) {
    try {
      const result = await this.edgeAIService.deployModelToEdge(
        deployModelDto.deviceId,
        deployModelDto.modelId,
        deployModelDto.configuration,
      );
      return {
        success: true,
        data: { deployed: result },
        message: 'AI模型部署成功',
      };
    } catch (error) {
      this.logger.error('部署AI模型失败', error);
      throw new HttpException(
        {
          success: false,
          message: '部署AI模型失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('inference')
  @ApiOperation({ summary: '执行边缘推理' })
  @ApiBody({ type: InferenceRequestDto })
  @ApiResponse({ status: 201, description: '推理执行成功' })
  @ApiResponse({ status: 400, description: '推理失败' })
  async performInference(@Body() inferenceRequestDto: InferenceRequestDto) {
    try {
      const result = await this.edgeAIService.performEdgeInference(inferenceRequestDto);
      return {
        success: true,
        data: result,
        message: '边缘推理执行成功',
      };
    } catch (error) {
      this.logger.error('执行边缘推理失败', error);
      throw new HttpException(
        {
          success: false,
          message: '执行边缘推理失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('learning/start')
  @ApiOperation({ summary: '启动分布式学习任务' })
  @ApiBody({ type: DistributedLearningTaskDto })
  @ApiResponse({ status: 201, description: '学习任务启动成功' })
  @ApiResponse({ status: 400, description: '启动失败' })
  async startDistributedLearning(@Body() learningTaskDto: DistributedLearningTaskDto) {
    try {
      const taskId = await this.edgeAIService.startDistributedLearning(learningTaskDto);
      return {
        success: true,
        data: { taskId },
        message: '分布式学习任务启动成功',
      };
    } catch (error) {
      this.logger.error('启动分布式学习任务失败', error);
      throw new HttpException(
        {
          success: false,
          message: '启动分布式学习任务失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('optimization')
  @ApiOperation({ summary: '实时决策优化' })
  @ApiBody({ type: OptimizationRequestDto })
  @ApiResponse({ status: 201, description: '优化完成' })
  @ApiResponse({ status: 400, description: '优化失败' })
  async optimizeDecision(@Body() optimizationRequestDto: OptimizationRequestDto) {
    try {
      const result = await this.edgeAIService.optimizeRealTimeDecision(
        optimizationRequestDto.context,
        optimizationRequestDto.constraints,
      );
      return {
        success: true,
        data: result,
        message: '实时决策优化完成',
      };
    } catch (error) {
      this.logger.error('实时决策优化失败', error);
      throw new HttpException(
        {
          success: false,
          message: '实时决策优化失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取边缘AI统计信息' })
  @ApiResponse({ status: 200, description: '统计信息获取成功' })
  async getStatistics() {
    try {
      const statistics = await this.edgeAIService.getEdgeAIStatistics();
      return {
        success: true,
        data: statistics,
        message: '边缘AI统计信息获取成功',
      };
    } catch (error) {
      this.logger.error('获取边缘AI统计信息失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取边缘AI统计信息失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('devices')
  @ApiOperation({ summary: '获取边缘设备列表' })
  @ApiQuery({ name: 'status', required: false, description: '设备状态过滤' })
  @ApiQuery({ name: 'type', required: false, description: '设备类型过滤' })
  @ApiResponse({ status: 200, description: '设备列表获取成功' })
  async getDevices(
    @Query('status') status?: string,
    @Query('type') type?: string,
  ) {
    try {
      const devices = await this.edgeAIService.getDeviceList({ status, type });
      return {
        success: true,
        data: devices,
        message: '边缘设备列表获取成功',
      };
    } catch (error) {
      this.logger.error('获取边缘设备列表失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取边缘设备列表失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('devices/:deviceId')
  @ApiOperation({ summary: '获取边缘设备详情' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiResponse({ status: 200, description: '设备详情获取成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async getDeviceDetails(@Param('deviceId') deviceId: string) {
    try {
      const device = await this.edgeAIService.getDeviceDetails(deviceId);
      return {
        success: true,
        data: device,
        message: '边缘设备详情获取成功',
      };
    } catch (error) {
      this.logger.error('获取边缘设备详情失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取边缘设备详情失败',
          error: error.message,
        },
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Put('devices/:deviceId/heartbeat')
  @ApiOperation({ summary: '更新设备心跳' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiResponse({ status: 200, description: '心跳更新成功' })
  async updateHeartbeat(@Param('deviceId') deviceId: string) {
    try {
      await this.edgeAIService.updateDeviceHeartbeat(deviceId);
      return {
        success: true,
        message: '设备心跳更新成功',
      };
    } catch (error) {
      this.logger.error('更新设备心跳失败', error);
      throw new HttpException(
        {
          success: false,
          message: '更新设备心跳失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete('devices/:deviceId')
  @ApiOperation({ summary: '注销边缘设备' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiResponse({ status: 200, description: '设备注销成功' })
  async unregisterDevice(@Param('deviceId') deviceId: string) {
    try {
      await this.edgeAIService.unregisterDevice(deviceId);
      return {
        success: true,
        message: '边缘设备注销成功',
      };
    } catch (error) {
      this.logger.error('注销边缘设备失败', error);
      throw new HttpException(
        {
          success: false,
          message: '注销边缘设备失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
